using System.Collections.Generic;
using System.Linq;
using UnityEngine;


public class TileManager
{
    // 添加瓦片类型缓存
    private Dictionary<int, List<Vector2Int>> _typePositionsCache = new();

    private PathFinder pathFinder;
    private int maxTurnPoint;
    private Board board;
    private int innerGridWidth;
    private int innerGridHeight;
    private int totalCanMatchTileCount;
    private int _remainingTileCount;

    // 相邻对调整器
    private AdjacentPairAdjuster adjacentPairAdjuster;
    public TileManager(PathFinder pathFinder, int maxTurnPoint, Board board, int innerGridWidth, int innerGridHeight)
    {
        this.pathFinder = pathFinder;
        this.maxTurnPoint = maxTurnPoint;
        this.board = board;
        this.innerGridWidth = innerGridWidth;
        this.innerGridHeight = innerGridHeight;
        this.adjacentPairAdjuster = new AdjacentPairAdjuster();
    }

    public void InitializeCache(int totalTileCount)
    {
        if (totalTileCount == 0)
        {
            totalCanMatchTileCount = 0;
            for (int x = 1; x <= innerGridWidth; x++)
            {
                for (int y = 1; y <= innerGridHeight; y++)
                {
                    var cell = board.grid[x, y];
                    if (cell != null && !cell.IsEmpty && cell.tile.CanMatch)
                    {
                        totalCanMatchTileCount++;
                    }
                }
            }
            _remainingTileCount = totalCanMatchTileCount;
        }
        else
        {
            totalCanMatchTileCount = totalTileCount;
            _remainingTileCount = 0;
            for (int x = 1; x <= innerGridWidth; x++)
            {
                for (int y = 1; y <= innerGridHeight; y++)
                {
                    var cell = board.grid[x, y];
                    if (cell != null && !cell.IsEmpty && cell.tile.CanMatch)
                    {
                        _remainingTileCount++;
                    }
                }
            }
        }



        RefreshTypeCache();
    }

    public void RefreshTypeCache()
    {
        _typePositionsCache.Clear();
        for (int x = 1; x < board.width; x++)
        {
            for (int y = 1; y < board.height; y++)
            {
                var cell = board.grid[x, y];
                if (cell != null && !cell.IsEmpty)
                {
                    UpdateTypeCache(cell.pos, cell.type);
                }
            }
        }
    }

    private void UpdateTypeCache(Vector2Int pos, int type)
    {
        if (!_typePositionsCache.ContainsKey(type))
            _typePositionsCache[type] = new List<Vector2Int>();

        _typePositionsCache[type].Add(pos);
    }

    private void RemoveFromTypeCache(Vector2Int pos, int type)
    {
        if (_typePositionsCache.ContainsKey(type))
        {
            _typePositionsCache[type].Remove(pos);
            if (_typePositionsCache[type].Count == 0)
            {
                _typePositionsCache.Remove(type);
            }
        }
    }

    // 优化后的匹配检测
    public bool HasValidMatch()
    {
        foreach (var typeGroup in _typePositionsCache.Where(g => g.Value.Count >= 2))
        {
            if (IgnoreCantMatchTile(typeGroup.Key))
                continue;
            var positions = typeGroup.Value;
            for (int i = 0; i < positions.Count; i++)
            {
                for (int j = i + 1; j < positions.Count; j++)
                {
                    var canConnect = pathFinder.CanConnect(positions[i], positions[j]);
                    if (canConnect)
                        return true;
                }
            }
        }
        return false;
    }

    // 获取一个可匹配的对子
    public (Vector2Int, Vector2Int)? FindMatchablePair()
    {
        foreach (var typeGroup in _typePositionsCache.Where(g => g.Value.Count >= 2))
        {
            if (IgnoreCantMatchTile(typeGroup.Key))
                continue;
            var positions = typeGroup.Value;
            for (int i = 0; i < positions.Count; i++)
            {
                var posA = positions[i];
                for (int j = i + 1; j < positions.Count; j++)
                {
                    var posB = positions[j];
                    var canConnect = pathFinder.CanConnect(posA, posB);
                    if (canConnect)
                    {
                        return (posA, posB);
                    }
                }
            }
        }
        return null;
    }

    /// <summary>
    ///  随机获取一组对子
    /// </summary>
    /// <returns></returns>
    public (Vector2Int, Vector2Int)? FindRandomPair()
    {
        var typeGroup = _typePositionsCache.Where(g => g.Key >= Tile3D.TYPE_BLOCK && g.Key <= Tile3D.TYPE_BLOCK_END && g.Value.Count >= 2);

        // 如果没有可匹配的类型组，返回 null
        if (!typeGroup.Any())
            return null;

        // 随机选择一个类型组
        var randomGroup = typeGroup.ElementAt(Random.Range(0, typeGroup.Count()));

        // 从该组中随机选择两个不同的位置
        var positions = randomGroup.Value;
        int index1 = Random.Range(0, positions.Count);
        int index2;
        do
        {
            index2 = Random.Range(0, positions.Count);
        } while (index1 == index2);

        return (positions[index1], positions[index2]);
    }

    /// <summary>
    /// 随机获取多组对子
    /// </summary>
    /// <param name="pairCount">需要获取的对子数量</param>
    /// <param name="avoidDuplicates">是否避免重复砖块</param>
    /// <returns>返回随机对子列表，每个对子包含两个位置坐标</returns>
    public List<(Vector2Int, Vector2Int)> FindRandomPairs(int pairCount, bool avoidDuplicates = true)
    {
        var result = new List<(Vector2Int, Vector2Int)>();
        var typeGroups = _typePositionsCache.Where(g => g.Key >= Tile3D.TYPE_BLOCK && g.Key <= Tile3D.TYPE_BLOCK_END && g.Value.Count >= 2).ToList();

        // 如果没有可匹配的类型组，返回空列表
        if (!typeGroups.Any())
            return result;

        // 用于跟踪已选择的砖块，避免重复
        HashSet<Vector2Int> selectedPositions = new HashSet<Vector2Int>();

        for (int i = 0; i < pairCount; i++)
        {
            bool foundNewPair = false;
            int retryCount = 0; // 重试计数器

            while (!foundNewPair && retryCount < 2) // 最多重试1次
            {
                // 如果所有类型组都已经用完，跳出循环
                if (!typeGroups.Any())
                    break;

                // 随机选择一个类型组
                int groupIndex = Random.Range(0, typeGroups.Count);
                var randomGroup = typeGroups[groupIndex];
                var positions = randomGroup.Value;

                // 如果该组中的可用位置不足两个，移除该组并继续
                if (positions.Count < 2)
                {
                    typeGroups.RemoveAt(groupIndex);
                    continue;
                }

                // 如果需要避免重复砖块
                if (avoidDuplicates)
                {
                    // 过滤掉已选择的位置
                    var availablePositions = positions.Where(p => !selectedPositions.Contains(p)).ToList();

                    // 如果可用位置不足两个，移除该组并继续
                    if (availablePositions.Count < 2)
                    {
                        typeGroups.RemoveAt(groupIndex);
                        continue;
                    }

                    // 随机选择两个不同的位置
                    int index1 = Random.Range(0, availablePositions.Count);
                    Vector2Int pos1 = availablePositions[index1];
                    availablePositions.RemoveAt(index1);

                    int index2 = Random.Range(0, availablePositions.Count);
                    Vector2Int pos2 = availablePositions[index2];

                    // 添加到结果列表
                    result.Add((pos1, pos2));

                    // 将选择的位置添加到已选择集合
                    selectedPositions.Add(pos1);
                    selectedPositions.Add(pos2);

                    foundNewPair = true;
                }
                else
                {
                    // 不需要避免重复，直接随机选择两个不同的位置
                    int index1 = Random.Range(0, positions.Count);
                    int index2;
                    do
                    {
                        index2 = Random.Range(0, positions.Count);
                    } while (index1 == index2);

                    // 添加到结果列表
                    result.Add((positions[index1], positions[index2]));
                    foundNewPair = true;
                }
            }

            // 如果重试后仍未找到新的对，则跳过此次循环
            if (!foundNewPair)
            {
                continue;
            }
        }

        return result;
    }

    /// <summary>
    /// 忽略不能匹配的tile
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    private bool IgnoreCantMatchTile(int type)
    {
        return type > Tile3D.TYPE_BLOCK_END && type != Tile3D.TYPE_FIRECRACKER;
    }

    // 计算拐点数量
    private int GetTurnPointCount(List<Vector2Int> pathPoints)
    {
        if (pathPoints == null || pathPoints.Count <= 2)
        {
            return 0;
        }

        int turnPointCount = 0;
        for (int i = 1; i < pathPoints.Count - 1; i++)
        {
            Vector2Int prevDirection = pathPoints[i] - pathPoints[i - 1];
            Vector2Int nextDirection = pathPoints[i + 1] - pathPoints[i];

            if (prevDirection != nextDirection)
            {
                turnPointCount++;
            }
        }

        return turnPointCount;
    }

    // 移除一对匹配的瓦片
    public void RemoveMatchedPair(Vector2Int pos1, Vector2Int pos2)
    {
        RemoveTile(pos1);
        RemoveTile(pos2);
    }

    public void RemoveTile(Vector2Int pos)
    {
        var cell = board.grid[pos.x, pos.y];
        if (cell != null && !cell.IsEmpty && cell.tile.CanMatch)
        {
            _remainingTileCount--;
        }

        var type = board.grid[pos.x, pos.y].type;
        RemoveFromTypeCache(pos, type);
        board.ClearBlock(pos);
    }

    #region  生成砖块数据
    private Dictionary<int, List<int>> sameTypeGroups = new(); // 用于存储相同类型的位置索引;
    private Dictionary<int, int> specialTileTypeDic = new();
    public int[] GenerateTileValues(int totalType, int[] boardInfo, int[] typeCounts, int matchingPairCount, int[] specialPairs)
    {
        var totalCount = innerGridWidth * innerGridHeight;
        var types = new int[totalCount];
        var randomTypes = new Dictionary<int, int>();//随机砖块，1~99+特殊属性(反面或藤曼)
        sameTypeGroups.Clear();//分组类型
        specialTileTypeDic.Clear();

        // 初始化板块信息
        if (boardInfo.Length > 0)
        {
            for (int i = 0; i < boardInfo.Length; i++)
            {
                var groupType = boardInfo[i];
                types[i] = groupType;

                if (groupType == Tile3D.TYPE_BLOCK || groupType == Tile3D.TYPE_QUESTION || groupType == Tile3D.TYPE_CHAIN)
                {
                    randomTypes.Add(i, groupType);
                }
                else if (groupType > Tile3D.TYPE_BLOCK && groupType <= Tile3D.TYPE_BLOCK_END)
                {
                    //类型分组存储位置
                    if (!sameTypeGroups.ContainsKey(groupType))
                    {
                        sameTypeGroups[groupType] = new List<int>();
                    }
                    sameTypeGroups[groupType].Add(i);
                }
            }
        }
        else
        {
            for (int i = 0; i < totalCount; i++)
            {
                types[i] = Tile3D.TYPE_BLOCK;
                randomTypes.Add(i, Tile3D.TYPE_BLOCK);
            }
        }

        //随机类型
        var typeAry = new List<int>();
        for (int i = 0; i < totalType; i++)
        {
            typeAry.Add(i + 1);
        }
        typeAry.Shuffle();


        //生成值
        var values = new List<int>();
        var count = 0;
        for (int i = 0; i < typeCounts.Length; i++)
        {
            if (typeAry.Count == 0 || count >= totalCount)
                break;
            var group = typeCounts[i];
            if (group == 0)
                continue;

            var createCount = (i + 1) * 2;//2,4,6,8,10
            for (int g = 0; g < group; g++)
            {
                if (typeAry.Count == 0 || count >= totalCount)
                    break;
                for (int j = 0; j < createCount; j++)
                {
                    values.Add(typeAry[0]);
                    count++;
                    if (count >= totalCount)
                        break;
                }
                typeAry.RemoveAt(0);
            }
        }

        //添加特殊对
        for (int i = 0; i < specialPairs.Length; i += 2)
        {
            var specialTileType = specialPairs[i];
            var specialTileCount = specialPairs[i + 1] * 2;
            specialTileTypeDic[specialTileType] = specialTileCount;
            for (int j = 0; j < specialTileCount; j++)
            {
                values.Add(specialTileType);
            }
        }
        values.Shuffle();//打乱

        //处理随机砖块
        var index = 0;
        foreach (var kv in randomTypes)
        {
            var pos = kv.Key;
            var tileType = types[pos];
            if (index < values.Count)
            {
                var value = values[index];
                if (tileType == Tile3D.TYPE_BLOCK)
                {
                    types[pos] = value;
                }
                else if (tileType > Tile3D.TYPE_BLOCK_END)
                {
                    if (specialTileTypeDic.ContainsKey(tileType) || value > Tile3D.TYPE_BLOCK_END)
                    {
                        types[pos] = value;
                    }
                    else
                    {
                        //特殊砖块 = 砖块类型+砖块值
                        types[pos] += value;
                    }
                }
                index++;
            }
            else
            {
                types[pos] = Tile3D.TYPE_BLOCK;
            }
        }

        // 处理相同类型砖块
        foreach (var group in sameTypeGroups)
        {
            int type = 1;
            if (typeAry.Count > 0)
            {
                var randomIdx = UnityEngine.Random.Range(0, typeAry.Count);
                type = typeAry[randomIdx];
                typeAry.RemoveAt(randomIdx);
            }
            foreach (var pos in group.Value)
            {
                types[pos] = type;
            }
        }

        if (matchingPairCount > 0)
        {
            var ignoreList = sameTypeGroups.SelectMany(g => g.Value).ToList();
            types = adjacentPairAdjuster.SetMatchingPairs(types, matchingPairCount, innerGridWidth, innerGridHeight, ignoreList, ConfigSetting.neighborPairOffset);
        }

        return types;
    }
    #endregion





















    public int GetNeighborPairCount()
    {
        var types = new int[innerGridWidth * innerGridHeight];
        for (int x = 1; x <= innerGridWidth; x++)
        {
            for (int y = 1; y <= innerGridHeight; y++)
            {
                var cell = board.grid[x, y];
                if (cell != null && !cell.IsEmpty)
                {
                    types[(y - 1) * innerGridWidth + (x - 1)] = cell.type;
                }
            }
        }
        var ignorePositions = new List<int>();
        for (int x = 1; x <= innerGridWidth; x++)
        {
            for (int y = 1; y <= innerGridHeight; y++)
            {
                var cell = board.grid[x, y];
                if (cell != null && !cell.IsEmpty && !cell.tile.CanMatch)
                {
                    ignorePositions.Add((y - 1) * innerGridWidth + (x - 1));
                }
            }
        }

        return adjacentPairAdjuster.CountMatchingPairs(types, innerGridWidth, innerGridHeight, true, ignorePositions);
    }

    #region  计算相邻数量

    #endregion

    // 检查是否还有可消除的瓦片
    public bool HasRemainingTiles()
    {
        return GetRemainingTileCount() > 0;
    }

    // 获取剩余瓦片数量
    public int GetRemainingTileCount()
    {
        return _remainingTileCount;
    }

    public float GetRemainingPercent()
    {
        if (totalCanMatchTileCount == 0)
            return 0;
        return (float)_remainingTileCount / totalCanMatchTileCount;
    }

    // 获取总瓦片数量
    public int GetTotalTileCount()
    {
        return totalCanMatchTileCount;
    }

    #region 洗牌逻辑

    // 根据剩余数量计算所需的相邻对百分比
    private float GetNeighborPercentByRemaining(int remainCount)
    {
        if (totalCanMatchTileCount == 0)
            return 0;

        // 计算剩余砖块百分比
        float remainingPercent = (float)remainCount / totalCanMatchTileCount;

        var info = ConfigGate.GetData(GameGlobal.EnterLevel);
        if (info == null)
            return 0;

        // 获取相邻对数百分比
        return info.GetNeighborPercentByRemaining(remainingPercent);
    }

    // 重新排列所有瓦片
    public void ShuffleTiles()
    {
        // 收集所有可洗牌瓦片及其位置
        List<Vector2Int> positions = new List<Vector2Int>();
        List<BoardCell> allTiles = new List<BoardCell>();
        for (int x = 1; x <= innerGridWidth; x++)
        {
            for (int y = 1; y <= innerGridHeight; y++)
            {
                var cell = board.grid[x, y];
                if (cell != null && !cell.IsEmpty && cell.tile != null && cell.tile.CanShuffle)
                {
                    positions.Add(new Vector2Int(x, y));
                    allTiles.Add(cell);
                }
            }
        }

        // 随机打乱瓦片
        allTiles.Shuffle();

        // 计算目标相邻对数
        var neighborSamePercent = GetNeighborPercentByRemaining(positions.Count);
        if (neighborSamePercent > 0)
        {
            var targetPairCount = Mathf.RoundToInt(positions.Count * neighborSamePercent / 2f); // 目标相邻对数
            Log.Info($"neighborSamePercent:{neighborSamePercent}  targetPairCount:{targetPairCount}");

            // 将瓦片类型转换为整型数组以供SetMatchingPairs使用
            int[] tileTypes = new int[innerGridWidth * innerGridHeight];
            var ignorePositions = new List<int>();

            // 填充完整的网格类型数组，包括空位置
            for (int x = 1; x <= innerGridWidth; x++)
            {
                for (int y = 1; y <= innerGridHeight; y++)
                {
                    int arrayIndex = (y - 1) * innerGridWidth + (x - 1);
                    var cell = board.grid[x, y];
                    if (cell != null && !cell.IsEmpty)
                    {
                        tileTypes[arrayIndex] = cell.type;
                        // 如果瓦片不能匹配，添加到忽略列表
                        if (!cell.tile.CanMatch)
                        {
                            ignorePositions.Add(arrayIndex);
                        }
                    }
                    else
                    {
                        tileTypes[arrayIndex] = Tile3D.TYPE_EMPTY;
                    }
                }
            }

            // 调用SetMatchingPairs来调整相邻对数（考虑忽略列表）
            int[] adjustedTypes = adjacentPairAdjuster.SetMatchingPairs(tileTypes, targetPairCount, innerGridWidth, innerGridHeight, ignorePositions);

            // 将调整后的类型应用回瓦片列表
            var remainingTiles = new List<BoardCell>(allTiles);
            for (int i = 0; i < positions.Count; i++)
            {
                var pos = positions[i];
                int arrayIndex = (pos.y - 1) * innerGridWidth + (pos.x - 1);
                int desiredType = adjustedTypes[arrayIndex];
                bool found = false;

                // 寻找与adjustedTypes[arrayIndex]匹配的瓦片并重新排列
                for (int j = 0; j < remainingTiles.Count; j++)
                {
                    if (remainingTiles[j].type == desiredType)
                    {
                        // 找到匹配类型的瓦片，将其放置在当前位置
                        // 并从剩余列表中移除该瓦片
                        allTiles[i] = remainingTiles[j];
                        remainingTiles.RemoveAt(j);
                        found = true;
                        break;
                    }
                }

                // 如果没有找到精确匹配的类型，选取第一个可用瓦片
                if (!found && remainingTiles.Count > 0)
                {
                    allTiles[i] = remainingTiles[0];
                    remainingTiles.RemoveAt(0);
                }
            }
        }

        // 使用最佳排列重新放置瓦片
        for (int i = 0; i < positions.Count; i++)
        {
            var pos = positions[i];
            var tile = allTiles[i];
            board.grid[pos.x, pos.y] = tile;
            tile.pos = pos;
        }

        // 刷新类型缓存
        RefreshTypeCache();
    }

    #endregion
}