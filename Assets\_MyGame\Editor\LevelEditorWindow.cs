using UnityEditor;
using UnityEngine;
using System.IO;
using IdiomMerge;
using UnityEngine.Networking;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net;

public class LevelEditorWindow : EditorWindow
{
    // 方块类型常量
    private static class TileTypes
    {
        public const int Empty = 0;
        public const int Brick = 1;
        public const int BrickEnd = 99;
        public const int Question = 100;
        public const int Freeze1 = 201;
        public const int Freeze2 = 202;
        public const int Freeze3 = 203;
        public const int Chain = 300;
        public const int Wood = 400;
        public const int Stone = 500;
        public const int Firecracker = 600;
    }

    private const float DRAG_THRESHOLD = 2f;

    private Vector2 scrollPosition;
    private LevelData currentLevel;
    private int currentTileType = TileTypes.Brick;

    private bool isDragging = false;
    private int lastDragButton = -1;
    private bool isMouseDown = false;
    private Vector2 mouseDownPosition;

    private int selectedWidth = 8;
    private int selectedHeight = 14;

    private int currentBrickValue = 1;

    // AI生成相关字段
    private string aiPatternDescription = "";
    private bool isGeneratingAI = false;
    private string geminiApiKey = "";
    private readonly string[] randomPatterns = { "蝴蝶", "花朵", "心形", "星星", "圆形", "菱形", "三角形", "十字形" };

    [MenuItem("Tools/连连看关卡编辑器")]
    public static void ShowWindow()
    {
        GetWindow<LevelEditorWindow>("连连看关卡编辑器");
    }

    private void OnEnable()
    {
        InitLevel();
    }

    private void InitLevel()
    {
        currentLevel = new LevelData
        {
            width = selectedWidth,
            height = selectedHeight,
            tileTypes = new int[selectedWidth * selectedHeight],
        };

        // 默认填充砖块
        for (int i = 0; i < currentLevel.tileTypes.Length; i++)
        {
            currentLevel.tileTypes[i] = TileTypes.Brick;
        }
    }

    private void OnGUI()
    {
        EditorGUILayout.BeginHorizontal();

        EditorGUILayout.BeginVertical(GUILayout.ExpandWidth(true));
        DrawGrid();
        EditorGUILayout.EndVertical();

        EditorGUILayout.BeginVertical(GUILayout.Width(300));
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        DrawBasicSettings();
        EditorGUILayout.Space(10);
        DrawTileTypeSelector();
        EditorGUILayout.Space(10);
        DrawAIGenerationSettings();
        EditorGUILayout.Space(10);
        DrawButtons();

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();

        EditorGUILayout.EndHorizontal();
    }

    private void DrawBasicSettings()
    {
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("基本设置", EditorStyles.boldLabel);

        EditorGUI.BeginChangeCheck();

        selectedWidth = EditorGUILayout.IntField("宽度", selectedWidth);
        selectedHeight = EditorGUILayout.IntField("高度", selectedHeight);

        if (EditorGUI.EndChangeCheck())
        {
            InitLevel();
        }
    }

    private void DrawTileTypeSelector()
    {
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("方块类型设置", EditorStyles.boldLabel);

        // 使用单选按钮组
        EditorGUILayout.BeginVertical("box");

        DrawTileTypeOption("空", TileTypes.Empty, GetTileColor(TileTypes.Empty));
        DrawTileTypeOption("砖块", TileTypes.Brick, GetTileColor(TileTypes.Brick));
        DrawTileTypeOption("问号", TileTypes.Question, GetTileColor(TileTypes.Question));
        DrawTileTypeOption("冰冻1", TileTypes.Freeze1, GetTileColor(TileTypes.Freeze1));
        DrawTileTypeOption("冰冻2", TileTypes.Freeze2, GetTileColor(TileTypes.Freeze2));
        DrawTileTypeOption("冰冻3", TileTypes.Freeze3, GetTileColor(TileTypes.Freeze3));
        DrawTileTypeOption("锁链", TileTypes.Chain, GetTileColor(TileTypes.Chain));
        DrawTileTypeOption("木块", TileTypes.Wood, GetTileColor(TileTypes.Wood));
        DrawTileTypeOption("石头", TileTypes.Stone, GetTileColor(TileTypes.Stone));
        DrawTileTypeOption("爆竹", TileTypes.Firecracker, GetTileColor(TileTypes.Firecracker));

        EditorGUILayout.EndVertical();
    }

    private void DrawAIGenerationSettings()
    {
        EditorGUILayout.LabelField("AI生成关卡", EditorStyles.boldLabel);
        EditorGUILayout.BeginVertical("box");

        // API密钥输入
        EditorGUILayout.LabelField("Gemini API密钥:");
        geminiApiKey = EditorGUILayout.PasswordField(geminiApiKey);

        EditorGUILayout.Space(5);

        // 图案描述输入
        EditorGUILayout.LabelField("图案描述:");
        EditorGUILayout.BeginHorizontal();
        aiPatternDescription = EditorGUILayout.TextField(aiPatternDescription);

        // 随机按钮
        if (GUILayout.Button("随机", GUILayout.Width(50)))
        {
            aiPatternDescription = randomPatterns[UnityEngine.Random.Range(0, randomPatterns.Length)];
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(5);

        // 生成按钮
        GUI.enabled = !isGeneratingAI && !string.IsNullOrEmpty(aiPatternDescription);//&& !string.IsNullOrEmpty(geminiApiKey)
        if (GUILayout.Button(isGeneratingAI ? "生成中..." : "AI生成布局", GUILayout.Height(30)))
        {
            GenerateAILayout();
        }
        GUI.enabled = true;

        // 测试按钮（用于演示）
        // if (GUILayout.Button("测试生成（演示用）", GUILayout.Height(25)))
        // {
        //     GenerateTestLayout();
        // }

        EditorGUILayout.EndVertical();
    }

    private void DrawTileTypeOption(string label, int tileType, Color color)
    {
        EditorGUILayout.BeginHorizontal();
        // 创建一个颜色预览框
        Rect colorRect = EditorGUILayout.GetControlRect(GUILayout.Width(20), GUILayout.Height(16));
        EditorGUI.DrawRect(colorRect, color);

        if (EditorGUILayout.ToggleLeft($"{label}({tileType})", currentTileType == tileType, GUILayout.Width(100)))
        {
            currentTileType = tileType;
        }

        // 只在砖块类型时显示数值输入框
        if (tileType == TileTypes.Brick)
        {
            GUI.enabled = currentTileType == TileTypes.Brick;
            currentBrickValue = Mathf.Clamp(EditorGUILayout.IntField(currentBrickValue, GUILayout.Width(50)), 1, 99);
            GUI.enabled = true;
        }

        EditorGUILayout.EndHorizontal();
    }

    private void DrawGrid()
    {
        float availableWidth = position.width * 0.7f - 20; // 留出一些边距
        float availableHeight = position.height - 20;

        // 计算单元格大小，使网格适应可用空间
        float cellWidth = availableWidth / currentLevel.width;
        float cellHeight = availableHeight / currentLevel.height;
        float cellSize = Mathf.Min(cellWidth, cellHeight);

        float gridWidth = currentLevel.width * cellSize;
        float gridHeight = currentLevel.height * cellSize;

        // 居中显示网格
        float offsetX = (availableWidth - gridWidth) * 0.5f;
        float offsetY = (availableHeight - gridHeight) * 0.5f;

        Rect gridRect = GUILayoutUtility.GetRect(availableWidth, availableHeight);

        // 绘制网格背景
        EditorGUI.DrawRect(new Rect(gridRect.x + offsetX, gridRect.y + offsetY, gridWidth, gridHeight), Color.gray);

        // 处理鼠标事件
        Event e = Event.current;
        if (e.type == EventType.MouseDown && e.button <= 1)
        {
            isMouseDown = true;
            mouseDownPosition = e.mousePosition;
            lastDragButton = e.button;
        }
        else if (e.type == EventType.MouseDrag && isMouseDown)
        {
            // 只有移动距离超过阈值才开始拖动
            if (!isDragging && Vector2.Distance(mouseDownPosition, e.mousePosition) > DRAG_THRESHOLD)
            {
                isDragging = true;
            }
        }
        else if (e.type == EventType.MouseUp)
        {
            isMouseDown = false;
            isDragging = false;
            lastDragButton = -1;
        }

        // 绘制网格线和方块
        float lineWidth = 2f;
        for (int y = 0; y < currentLevel.height; y++)
        {
            for (int x = 0; x < currentLevel.width; x++)
            {
                Rect cellRect = new Rect(
                    gridRect.x + offsetX + x * cellSize,
                    gridRect.y + offsetY + y * cellSize,
                    cellSize,
                    cellSize
                );
                int tileIndex = y * currentLevel.width + x;

                // 绘制方块
                if (currentLevel.tileTypes[tileIndex] > 0)
                {
                    EditorGUI.DrawRect(cellRect, GetTileColor(currentLevel.tileTypes[tileIndex]));
                    GUI.Label(cellRect, currentLevel.tileTypes[tileIndex].ToString(), new GUIStyle()
                    {
                        normal = new GUIStyleState() { textColor = Color.white },
                        alignment = TextAnchor.MiddleCenter,
                        fontSize = Mathf.Max(12, Mathf.FloorToInt(cellSize * 0.4f))
                    });
                }

                // 绘制网格线
                Handles.color = Color.black;
                // 绘制水平线
                Vector3[] horizontalLinePoints = new Vector3[] {
                    new Vector3(cellRect.x, cellRect.y),
                    new Vector3(cellRect.x + cellSize, cellRect.y)
                };
                Handles.DrawAAPolyLine(lineWidth, horizontalLinePoints);

                // 绘制垂直线
                Vector3[] verticalLinePoints = new Vector3[] {
                    new Vector3(cellRect.x, cellRect.y),
                    new Vector3(cellRect.x, cellRect.y + cellSize)
                };
                Handles.DrawAAPolyLine(lineWidth, verticalLinePoints);

                // 处理点击和拖动
                if ((e.type == EventType.MouseDown || (isDragging && e.type == EventType.MouseDrag)) &&
                    cellRect.Contains(e.mousePosition))
                {
                    int button = isDragging ? lastDragButton : e.button;
                    if (button == 0) // 左键点击或拖动
                    {
                        // 如果已经是同类型的特殊方块，则变回砖块
                        if (!isDragging && currentLevel.tileTypes[tileIndex] == currentTileType && currentTileType != TileTypes.Empty)
                        {
                            if (currentTileType == TileTypes.Brick)
                            {
                                currentLevel.tileTypes[tileIndex] = currentBrickValue;
                            }
                            else
                            {
                                currentLevel.tileTypes[tileIndex] = TileTypes.Brick;
                            }
                        }
                        else
                        {
                            currentLevel.tileTypes[tileIndex] = currentTileType;
                            // 如果是砖块类型，则设置对应的数值
                            if (currentTileType == TileTypes.Brick)
                            {
                                currentLevel.tileTypes[tileIndex] = currentBrickValue;
                            }
                        }
                        Repaint();
                        e.Use(); // 标记事件已处理
                    }
                    else if (button == 1) // 右键点击或拖动
                    {
                        currentLevel.tileTypes[tileIndex] = TileTypes.Empty;
                        Repaint();
                        e.Use(); // 标记事件已处理
                    }
                }
            }
        }

        // 绘制最后一条竖线
        Vector3[] lastVerticalLinePoints = new Vector3[] {
            new Vector3(gridRect.x + offsetX + gridWidth, gridRect.y + offsetY),
            new Vector3(gridRect.x + offsetX + gridWidth, gridRect.y + offsetY + gridHeight)
        };
        Handles.DrawAAPolyLine(lineWidth, lastVerticalLinePoints);

        // 绘制最后一条横线
        Vector3[] lastHorizontalLinePoints = new Vector3[] {
            new Vector3(gridRect.x + offsetX, gridRect.y + offsetY + gridHeight),
            new Vector3(gridRect.x + offsetX + gridWidth, gridRect.y + offsetY + gridHeight)
        };
        Handles.DrawAAPolyLine(lineWidth, lastHorizontalLinePoints);
    }

    private Color GetTileColor(int tileType)
    {
        if (tileType == TileTypes.Brick)
        {
            return new Color(0.7f, 0.95f, 0.7f);
        }
        if (tileType > TileTypes.Brick && tileType <= TileTypes.BrickEnd)
        {
            return new Color(0.5f, 0.95f, 0.5f);
        }

        switch (tileType)
        {
            case TileTypes.Empty:
                return Color.white;
            case TileTypes.Question:
                return Color.yellow;
            case TileTypes.Freeze1:
            case TileTypes.Freeze2:
            case TileTypes.Freeze3:
                return Color.cyan;
            case TileTypes.Chain:
                return new Color(0.8f, 0.8f, 0.8f);
            case TileTypes.Wood:
                return new Color(0.7f, 0.4f, 0.3f);
            case TileTypes.Stone:
                return Color.gray;
            case TileTypes.Firecracker:
                return new Color(1.0f, 0.4f, 0.1f);
            default:
                return Color.white;
        }
    }

    private void DrawButtons()
    {
        EditorGUILayout.Space(10);

        GUILayoutOption[] buttonOptions = { GUILayout.Height(30) };

        if (GUILayout.Button("清空方块", buttonOptions))
        {
            if (EditorUtility.DisplayDialog("确认", "是否确定清空所有方块？", "确定", "取消"))
            {
                for (int i = 0; i < currentLevel.tileTypes.Length; i++)
                {
                    currentLevel.tileTypes[i] = TileTypes.Brick;
                }
                Repaint();
            }
        }

        if (GUILayout.Button("保存关卡", buttonOptions))
        {
            if (ValidateLevel())
            {
                SaveLevel();
            }
        }

        if (GUILayout.Button("加载关卡", buttonOptions))
        {
            LoadLevel();
        }
    }

    private bool ValidateLevel()
    {
        // 只统计砖块、问号和锁链的数量
        int matchableCount = 0;
        foreach (int type in currentLevel.tileTypes)
        {
            if ((type >= TileTypes.Brick && type <= TileTypes.BrickEnd) || type == TileTypes.Question || type == TileTypes.Chain)
            {
                matchableCount++;
            }
        }

        bool isValid = matchableCount % 2 == 0;
        if (!isValid)
        {
            EditorUtility.DisplayDialog("验证失败", $"可配对方块（砖块、问号、锁链）的总数为 {matchableCount}，不是偶数", "确定");
        }

        return isValid;
    }

    private void SaveLevel()
    {
        // 创建关卡数据字符串
        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        sb.Append($"{currentLevel.width},{currentLevel.height}");

        for (int i = 0; i < currentLevel.tileTypes.Length; i++)
        {
            sb.Append($",{currentLevel.tileTypes[i]}");
        }
        EditorGUIUtility.systemCopyBuffer = sb.ToString();
        Debug.Log("数据已复制到剪贴板");

        // LevelDataWindow.ShowWindow(sb.ToString());
    }

    // 关卡数据显示窗口
    public class LevelDataWindow : EditorWindow
    {
        private string levelData;
        private Vector2 scrollPosition;
        private bool isLoadMode;
        private LevelEditorWindow editorWindow;

        public static void ShowWindow(string data, bool isLoad = false, LevelEditorWindow editor = null)
        {
            LevelDataWindow window = GetWindow<LevelDataWindow>("关卡数据");
            window.levelData = data;
            window.isLoadMode = isLoad;
            window.editorWindow = editor;
            window.minSize = new Vector2(400, 300);
            window.Show();
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("关卡数据", EditorStyles.boldLabel);
            EditorGUILayout.Space(10);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            GUI.SetNextControlName("LevelDataField");

            // 创建支持自动换行的文本区域样式
            GUIStyle textAreaStyle = new GUIStyle(EditorStyles.textArea)
            {
                wordWrap = true
            };

            levelData = EditorGUILayout.TextArea(levelData, textAreaStyle, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space(10);
            if (GUILayout.Button(isLoadMode ? "读取数据" : "复制到剪贴板"))
            {
                if (isLoadMode)
                {
                    LoadLevelFromString(levelData);
                }
                else
                {
                    EditorGUIUtility.systemCopyBuffer = levelData;
                    Debug.Log("数据已复制到剪贴板");
                }
            }
        }

        private void LoadLevelFromString(string data)
        {
            try
            {
                string[] parts = data.Split(',');
                if (parts.Length < 2)
                {
                    Debug.LogError("数据格式错误");
                    return;
                }

                int width = int.Parse(parts[0]);
                int height = int.Parse(parts[1]);
                int[] tileTypes = new int[width * height];

                // 读取方块类型
                for (int i = 0; i < width * height && i + 2 < parts.Length; i++)
                {
                    tileTypes[i] = int.Parse(parts[i + 2]);
                }

                // 更新编辑器窗口
                if (editorWindow != null)
                {
                    editorWindow.LoadLevelData(width, height, tileTypes);
                    Debug.Log("关卡数据加载成功");
                    this.Close();
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"加载关卡数据失败: {e.Message}");
            }
        }

        private void OnEnable()
        {
            EditorApplication.delayCall += () =>
            {
                GUI.FocusControl("LevelDataField");
            };
        }
    }

    // 供LevelDataWindow调用的加载方法
    public void LoadLevelData(int width, int height, int[] tileTypes)
    {
        selectedWidth = width;
        selectedHeight = height;
        currentLevel = new LevelData
        {
            width = width,
            height = height,
            tileTypes = tileTypes,
        };
        Repaint();
    }

    private void LoadLevel()
    {
        LevelDataWindow.ShowWindow("", true, this);
    }

    #region AI生成功能

    private void GenerateAILayout()
    {
        if (string.IsNullOrEmpty(aiPatternDescription))//string.IsNullOrEmpty(geminiApiKey) || 
        {
            EditorUtility.DisplayDialog("错误", "请输入API密钥和图案描述", "确定");
            return;
        }

        isGeneratingAI = true;
        CallGeminiAPI();
    }

    private async void CallGeminiAPI()
    {
        string prompt = CreatePrompt();
        string jsonData = CreateRequestJson(prompt);

        // 使用正确的Gemini API端点
        string apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent";

        Debug.Log($"API URL: {apiUrl}");
        Debug.Log($"请求JSON: {jsonData}");
        Debug.Log($"API Key: {geminiApiKey.Substring(0, 10)}...");

        using (UnityWebRequest request = new UnityWebRequest(apiUrl, "POST"))
        {
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/json");
            request.SetRequestHeader("x-goog-api-key", geminiApiKey);

            var operation = request.SendWebRequest();

            // 等待请求完成
            while (!operation.isDone)
            {
                await Task.Delay(100);
            }

            isGeneratingAI = false;

            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    ProcessAIResponse(request.downloadHandler.text);
                }
                catch (System.Exception e)
                {
                    EditorUtility.DisplayDialog("错误", $"解析AI响应失败: {e.Message}", "确定");
                    Debug.LogError($"AI响应解析错误: {e.Message}");
                }
            }
            else
            {
                string errorDetails = $"错误: {request.error}\n响应码: {request.responseCode}\n响应内容: {request.downloadHandler.text}";
                EditorUtility.DisplayDialog("错误", $"API调用失败: {request.error}", "确定");
                Debug.LogError($"Gemini API调用失败: {errorDetails}");
            }

            Repaint();
        }
    }

    private string CreatePrompt()
    {
        return $@"请根据描述""{aiPatternDescription}""生成一个{selectedWidth}x{selectedHeight}的连连看关卡布局。

要求：
1. 返回格式必须是纯数字数组，用逗号分隔，不要任何其他文字说明
2. 数组长度必须是{selectedWidth * selectedHeight}个数字
3. 数字含义：0=空格，1=普通砖块，100=问号块，300=锁链，400=木块，500=石头，600=爆竹
4. 布局要形成{aiPatternDescription}的形状，空格(0)用来勾勒形状轮廓
5. 确保可配对方块（1,100,300）的总数是偶数
6. 优先使用普通砖块(1)来填充图案主体

示例输出格式：
0,0,1,1,1,0,0,0,1,1,1,1,1,1,0,0,0,1,1,1,0,0

请直接返回数字数组，不要任何解释文字。";
    }

    private string CreateRequestJson(string prompt)
    {
        // Gemini API的正确格式
        var requestData = new
        {
            contents = new[]
            {
                new
                {
                    parts = new[]
                    {
                        new { text = prompt }
                    }
                }
            }
        };

        return JsonUtility.ToJson(requestData);
    }

    private void ProcessAIResponse(string responseText)
    {
        Debug.Log($"AI响应: {responseText}");

        // 解析Gemini API响应
        var response = JsonUtility.FromJson<GeminiResponse>(responseText);
        if (response?.candidates != null && response.candidates.Length > 0 &&
            response.candidates[0].content?.parts != null && response.candidates[0].content.parts.Length > 0)
        {
            string content = response.candidates[0].content.parts[0].text.Trim();

            // 提取数字数组
            string[] parts = content.Split(',');
            if (parts.Length == selectedWidth * selectedHeight)
            {
                int[] newTileTypes = new int[parts.Length];
                bool parseSuccess = true;

                for (int i = 0; i < parts.Length; i++)
                {
                    if (!int.TryParse(parts[i].Trim(), out newTileTypes[i]))
                    {
                        parseSuccess = false;
                        break;
                    }
                }

                if (parseSuccess)
                {
                    // 验证可配对方块数量
                    int matchableCount = 0;
                    foreach (int type in newTileTypes)
                    {
                        if ((type >= TileTypes.Brick && type <= TileTypes.BrickEnd) ||
                            type == TileTypes.Question || type == TileTypes.Chain)
                        {
                            matchableCount++;
                        }
                    }

                    if (matchableCount % 2 == 0)
                    {
                        // 应用新布局
                        currentLevel.tileTypes = newTileTypes;
                        Repaint();
                        EditorUtility.DisplayDialog("成功", $"AI生成的{aiPatternDescription}布局已应用！", "确定");
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("错误", "AI生成的布局中可配对方块数量不是偶数，请重新生成", "确定");
                    }
                }
                else
                {
                    EditorUtility.DisplayDialog("错误", "AI响应格式错误，无法解析数字", "确定");
                }
            }
            else
            {
                EditorUtility.DisplayDialog("错误", $"AI返回的数据长度不匹配，期望{selectedWidth * selectedHeight}个数字，实际{parts.Length}个", "确定");
            }
        }
        else
        {
            EditorUtility.DisplayDialog("错误", "AI响应格式错误", "确定");
        }
    }

    private void GenerateTestLayout()
    {
        // 生成一个简单的心形图案作为演示
        string testPattern = aiPatternDescription.ToLower();
        int[] testLayout = new int[selectedWidth * selectedHeight];

        // 根据不同图案生成不同的测试布局
        switch (testPattern)
        {
            case "心形":
                GenerateHeartPattern(testLayout);
                break;
            case "蝴蝶":
                GenerateButterflyPattern(testLayout);
                break;
            case "花朵":
                GenerateFlowerPattern(testLayout);
                break;
            case "星星":
                GenerateStarPattern(testLayout);
                break;
            default:
                GenerateDefaultPattern(testLayout);
                break;
        }

        // 应用测试布局
        currentLevel.tileTypes = testLayout;
        Repaint();
        EditorUtility.DisplayDialog("测试完成", $"已生成{testPattern}测试布局", "确定");
    }

    private void GenerateHeartPattern(int[] layout)
    {
        // 简单的心形图案 (8x14网格)
        int[] heartPattern = {
            0,0,0,0,0,0,0,0,
            0,1,1,0,0,1,1,0,
            1,1,1,1,1,1,1,1,
            1,1,1,1,1,1,1,1,
            0,1,1,1,1,1,1,0,
            0,0,1,1,1,1,0,0,
            0,0,0,1,1,0,0,0,
            0,0,0,0,0,0,0,0,
            0,0,0,0,0,0,0,0,
            0,0,0,0,0,0,0,0,
            0,0,0,0,0,0,0,0,
            0,0,0,0,0,0,0,0,
            0,0,0,0,0,0,0,0,
            0,0,0,0,0,0,0,0
        };

        for (int i = 0; i < layout.Length && i < heartPattern.Length; i++)
        {
            layout[i] = heartPattern[i];
        }
    }

    private void GenerateButterflyPattern(int[] layout)
    {
        // 简单的蝴蝶图案
        for (int i = 0; i < layout.Length; i++)
        {
            int x = i % selectedWidth;
            int y = i / selectedWidth;
            int centerX = selectedWidth / 2;
            int centerY = selectedHeight / 2;

            // 蝴蝶翅膀
            if ((x < centerX - 1 || x > centerX + 1) &&
                y > centerY - 3 && y < centerY + 3 &&
                Mathf.Abs(y - centerY) < 3 - Mathf.Abs(x - centerX) / 2)
            {
                layout[i] = TileTypes.Brick;
            }
            // 蝴蝶身体
            else if (x == centerX && y >= centerY - 2 && y <= centerY + 2)
            {
                layout[i] = TileTypes.Question;
            }
            else
            {
                layout[i] = TileTypes.Empty;
            }
        }
    }

    private void GenerateFlowerPattern(int[] layout)
    {
        // 简单的花朵图案
        for (int i = 0; i < layout.Length; i++)
        {
            int x = i % selectedWidth;
            int y = i / selectedWidth;
            int centerX = selectedWidth / 2;
            int centerY = selectedHeight / 2;

            float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));

            // 花心
            if (distance <= 1.5f)
            {
                layout[i] = TileTypes.Question;
            }
            // 花瓣
            else if (distance <= 3f && (x == centerX || y == centerY || Mathf.Abs(x - centerX) == Mathf.Abs(y - centerY)))
            {
                layout[i] = TileTypes.Brick;
            }
            else
            {
                layout[i] = TileTypes.Empty;
            }
        }
    }

    private void GenerateStarPattern(int[] layout)
    {
        // 简单的星星图案
        for (int i = 0; i < layout.Length; i++)
        {
            int x = i % selectedWidth;
            int y = i / selectedWidth;
            int centerX = selectedWidth / 2;
            int centerY = selectedHeight / 2;

            // 星星的五个角
            if ((x == centerX && Mathf.Abs(y - centerY) <= 3) ||
                (y == centerY && Mathf.Abs(x - centerX) <= 3) ||
                (Mathf.Abs(x - centerX) == Mathf.Abs(y - centerY) && Mathf.Abs(x - centerX) <= 2))
            {
                layout[i] = TileTypes.Brick;
            }
            else
            {
                layout[i] = TileTypes.Empty;
            }
        }
    }

    private void GenerateDefaultPattern(int[] layout)
    {
        // 默认的简单图案
        for (int i = 0; i < layout.Length; i++)
        {
            int x = i % selectedWidth;
            int y = i / selectedWidth;

            // 创建一个简单的边框图案
            if (x == 0 || x == selectedWidth - 1 || y == 0 || y == selectedHeight - 1 ||
                (x > 2 && x < selectedWidth - 3 && y > 2 && y < selectedHeight - 3))
            {
                layout[i] = TileTypes.Brick;
            }
            else
            {
                layout[i] = TileTypes.Empty;
            }
        }
    }

    #endregion

    #region AI响应数据结构

    [System.Serializable]
    public class GeminiResponse
    {
        public GeminiCandidate[] candidates;
    }

    [System.Serializable]
    public class GeminiCandidate
    {
        public GeminiContent content;
    }

    [System.Serializable]
    public class GeminiContent
    {
        public GeminiPart[] parts;
    }

    [System.Serializable]
    public class GeminiPart
    {
        public string text;
    }

    #endregion
}
