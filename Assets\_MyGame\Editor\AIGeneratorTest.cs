using UnityEngine;
using UnityEditor;

public class AIGeneratorTest : EditorWindow
{
    [MenuItem("Tools/测试AI生成器JSON")]
    public static void TestJSON()
    {
        // 测试请求JSON生成
        var requestData = new
        {
            model = "gemini-2.5-flash",
            messages = new[]
            {
                new { role = "user", content = "测试内容" }
            }
        };

        string json = JsonUtility.ToJson(requestData);
        Debug.Log($"请求JSON: {json}");

        // 测试响应JSON解析
        string testResponse = @"{
            ""choices"": [
                {
                    ""message"": {
                        ""content"": ""0,0,1,1,1,0,0,0,1,1,1,1,1,1,0,0,0,1,1,1,0,0""
                    }
                }
            ]
        }";

        var response = JsonUtility.FromJson<LevelEditorWindow.GeminiResponse>(testResponse);
        if (response?.choices != null && response.choices.Length > 0)
        {
            Debug.Log($"解析成功，内容: {response.choices[0].message.content}");
        }
        else
        {
            Debug.LogError("解析失败");
        }
    }
}
