using UnityEngine;
using UnityEditor;

public class AIGeneratorTest : EditorWindow
{
    [MenuItem("Tools/测试AI生成器JSON")]
    public static void TestJSON()
    {
        // 测试Gemini API请求JSON生成
        var requestData = new
        {
            contents = new[]
            {
                new
                {
                    parts = new[]
                    {
                        new { text = "根据描述\"心形\"生成一个8x14的连连看关卡布局。返回格式必须是纯数字数组，用逗号分隔。" }
                    }
                }
            }
        };

        string json = JsonUtility.ToJson(requestData);
        Debug.Log($"Gemini请求JSON: {json}");

        // 测试Gemini响应JSON解析
        string testResponse = @"{
            ""candidates"": [
                {
                    ""content"": {
                        ""parts"": [
                            {
                                ""text"": ""0,0,1,1,1,0,0,0,1,1,1,1,1,1,0,0,0,1,1,1,0,0""
                            }
                        ]
                    }
                }
            ]
        }";

        var response = JsonUtility.FromJson<LevelEditorWindow.GeminiResponse>(testResponse);
        if (response?.candidates != null && response.candidates.Length > 0 &&
            response.candidates[0].content?.parts != null && response.candidates[0].content.parts.Length > 0)
        {
            Debug.Log($"解析成功，内容: {response.candidates[0].content.parts[0].text}");
        }
        else
        {
            Debug.LogError("解析失败");
        }
    }
}
