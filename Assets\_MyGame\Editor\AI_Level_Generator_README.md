# AI关卡生成器使用说明

## 功能概述
在关卡编辑器中新增了AI生成关卡布局的功能，可以根据用户描述的图案自动生成对应形状的关卡布局。

## 使用步骤

### 1. 打开关卡编辑器
- 在Unity编辑器中，选择菜单 `Tools > 连连看关卡编辑器`

### 2. 配置AI生成设置
在编辑器右侧面板中找到"AI生成关卡"区域：

#### 2.1 输入Gemini API密钥
- 在"Gemini API密钥"字段中输入你的API密钥
- 获取密钥：访问 [Google AI Studio](https://aistudio.google.com/) 获取免费的Gemini API密钥

#### 2.2 设置图案描述
- 在"图案描述"字段中输入想要生成的图案，如：
  - "蝴蝶"
  - "花朵" 
  - "心形"
  - "星星"
  - "圆形"
  - "菱形"
  - "三角形"
  - "十字形"
- 或点击"随机"按钮自动选择一个图案

### 3. 生成布局
- 点击"AI生成布局"按钮
- 等待AI处理（按钮会显示"生成中..."）
- 生成完成后，新的布局会自动应用到编辑器中

## 技术细节

### API调用
- 使用Gemini 2.5 Flash模型
- 请求格式：OpenAI兼容的Chat Completions API
- 端点：`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`

### 生成规则
AI会根据以下规则生成布局：
1. 使用空格(0)勾勒图案轮廓
2. 使用普通砖块(1)填充图案主体
3. 确保可配对方块总数为偶数
4. 支持的方块类型：
   - 0: 空格
   - 1: 普通砖块
   - 100: 问号块
   - 300: 锁链
   - 400: 木块
   - 500: 石头
   - 600: 爆竹

### 验证机制
- 自动验证返回数据长度是否匹配网格尺寸
- 检查可配对方块数量是否为偶数
- 解析失败时会显示错误提示

## 注意事项

1. **API密钥安全**：请妥善保管你的API密钥，不要在代码中硬编码
2. **网络连接**：需要稳定的网络连接才能调用API
3. **生成质量**：AI生成的结果可能需要手动调整以达到最佳效果
4. **API限制**：注意Gemini API的使用限制和配额

## 故障排除

### 常见错误
1. **"API调用失败"**：检查网络连接和API密钥是否正确
2. **"解析AI响应失败"**：AI返回格式不正确，可以重新尝试
3. **"可配对方块数量不是偶数"**：AI生成的布局不符合游戏规则，需要重新生成

### 调试信息
- 所有API响应都会在Unity Console中记录
- 可以查看详细的错误信息进行问题定位

## 扩展功能
未来可以考虑添加：
- 更多预设图案模板
- 布局复杂度控制
- 特殊方块比例设置
- 批量生成功能
